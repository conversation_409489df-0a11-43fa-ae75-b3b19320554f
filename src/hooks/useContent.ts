import { useState, useEffect } from 'react';

// Configuration de l'API
const API_BASE_URL = 'https://afripath-cms-api.badoo-7.workers.dev';

interface ContentBlock {
  [key: string]: any;
}

interface PageContent {
  [blockId: string]: ContentBlock;
}

interface UseContentReturn {
  content: PageContent | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

// Hook pour charger le contenu d'une page
export const useContent = (page: string): UseContentReturn => {
  const [content, setContent] = useState<PageContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchContent = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`${API_BASE_URL}/content/${page}`);
      
      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setContent(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
      console.error('Erreur lors du chargement du contenu:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (page) {
      fetchContent();
    }
  }, [page]);

  return {
    content,
    loading,
    error,
    refetch: fetchContent
  };
};

// Hook pour charger tout le contenu du site
export const useAllContent = () => {
  const [content, setContent] = useState<{ [page: string]: PageContent } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAllContent = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`${API_BASE_URL}/content`);
      
      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setContent(data.pages || {});
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
      console.error('Erreur lors du chargement du contenu:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllContent();
  }, []);

  return {
    content,
    loading,
    error,
    refetch: fetchAllContent
  };
};

// Fonction utilitaire pour obtenir une valeur de contenu avec fallback
export const getContentValue = (
  content: PageContent | null,
  blockId: string,
  field: string,
  fallback: string = ''
): string => {
  if (!content || !content[blockId] || !content[blockId][field]) {
    return fallback;
  }
  
  const value = content[blockId][field];
  return typeof value === 'string' ? value : fallback;
};

// Fonction utilitaire pour obtenir un tableau de contenu avec fallback
export const getContentArray = (
  content: PageContent | null,
  blockId: string,
  field: string,
  fallback: any[] = []
): any[] => {
  if (!content || !content[blockId] || !content[blockId][field]) {
    return fallback;
  }
  
  const value = content[blockId][field];
  return Array.isArray(value) ? value : fallback;
};

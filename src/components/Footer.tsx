import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Linkedin, Facebook } from 'lucide-react';

const Footer = () => {
  const quickLinks = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Services', href: '/services' },
    { name: 'Country Profiles', href: '/country-profiles' },
    { name: 'Book Consultation', href: '/book-consultation' },
  ];

  return (
    <footer className="bg-[#1a2e1a] text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div>
            <Link to="/" className="flex items-center mb-4">
              <img
                src="/logo.png"
                alt="Afripath Consulting Logo"
                className="h-16 w-auto object-contain"
              />
            </Link>
            <p className="text-gray-300 text-sm leading-relaxed">
              Helping African diaspora professionals and entrepreneurs connect with verified 
              business opportunities, partners, and legal guidance across Africa.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link 
                    to={link.href} 
                    className="text-gray-300 hover:text-[#f4a261] transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact & Social */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Connect With Us</h3>
            <div className="space-y-2 text-gray-300 text-sm mb-4">
              <p><strong>Address:</strong> 539 W. Commerce St #7016, Dallas, TX 75208</p>
              <p><strong>Phone:</strong> +****************</p>
              <p><strong>Email:</strong> <EMAIL></p>
            </div>
            <div className="flex space-x-4">
              <a
                href="https://www.linkedin.com/company/afripath-consulting/about/?viewAsMember=true"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-[#f4a261] transition-colors duration-200"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href="https://www.facebook.com/profile.php?id=61577030893690&sk=mentions"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-[#f4a261] transition-colors duration-200"
              >
                <Facebook className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2025 Afripath Consulting. All rights reserved.
          </p>
          <div className="flex space-x-4 mt-4 sm:mt-0">
            <Link to="/privacy-policy" className="text-gray-400 hover:text-[#f4a261] text-sm transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link to="/terms-of-service" className="text-gray-400 hover:text-[#f4a261] text-sm transition-colors duration-200">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
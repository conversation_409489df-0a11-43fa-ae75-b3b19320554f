import React from 'react';

const BackgroundAnimation = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Floating particles */}
      <div className="absolute inset-0">
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 10}s`,
              animationDuration: `${8 + Math.random() * 4}s`,
            }}
          />
        ))}
      </div>

      {/* Geometric shapes */}
      <div className="absolute top-20 left-10 w-32 h-32 border border-white/10 rounded-full animate-pulse-slow" />
      <div className="absolute bottom-20 right-10 w-24 h-24 border border-[#f4a261]/30 rounded-full animate-pulse-slow" style={{ animationDelay: '2s' }} />
      <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white/15 rotate-45 animate-spin-slow" />
      <div className="absolute top-1/3 right-1/3 w-20 h-20 border border-[#f4a261]/20 rounded-full animate-pulse-slow" style={{ animationDelay: '4s' }} />

      {/* Gradient orbs */}
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-[#f4a261]/10 to-transparent rounded-full blur-3xl animate-float-slow" />
      <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-gradient-to-r from-white/10 to-transparent rounded-full blur-2xl animate-float-reverse" />
      <div className="absolute top-1/2 right-1/2 w-48 h-48 bg-gradient-to-r from-[#f4a261]/5 to-transparent rounded-full blur-xl animate-float" style={{ animationDelay: '3s' }} />

      {/* Moving lines */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse-slow" />
      <div className="absolute bottom-0 right-0 w-full h-px bg-gradient-to-l from-transparent via-[#f4a261]/30 to-transparent animate-pulse-slow" style={{ animationDelay: '1s' }} />
    </div>
  );
};

export default BackgroundAnimation;
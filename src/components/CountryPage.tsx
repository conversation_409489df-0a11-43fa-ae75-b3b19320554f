import React from 'react';
import { ArrowLeft, MapPin, TrendingUp, Users, Building } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useContent, getContentValue, getContentArray } from '../hooks/useContent';

interface CountryPageProps {
  countryKey: string;
  imagePath: string;
  statusColor?: string;
  statusText?: string;
}

const CountryPage: React.FC<CountryPageProps> = ({ 
  countryKey, 
  imagePath, 
  statusColor = "blue-500", 
  statusText = "Stable" 
}) => {
  const { content, loading, error } = useContent(countryKey);

  // Afficher un loader pendant le chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2d5016] mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du contenu...</p>
        </div>
      </div>
    );
  }

  if (error) {
    console.warn('Erreur de chargement du contenu:', error);
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/country-profiles"
            className="inline-flex items-center text-[#2d5016] hover:text-[#f4a261] mb-8 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Country Profiles
          </Link>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Image */}
            <div className="relative">
              <img
                src={imagePath}
                alt={getContentValue(content, 'hero', 'title', 'Country')}
                className="w-full h-80 object-cover rounded-xl shadow-lg"
              />
              <div className="absolute top-4 right-4">
                <div className={`px-3 py-1 rounded-full text-xs font-semibold bg-${statusColor} text-white`}>
                  {statusText}
                </div>
              </div>
            </div>

            {/* Right Column - Text */}
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-[#2d5016] mb-4">
                {getContentValue(content, 'hero', 'title', 'Country')}
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                {getContentValue(content, 'hero', 'subtitle', 'Economic Hub')}
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                {getContentValue(content, 'hero', 'description', 'Country description')}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Economic Overview */}
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-[#2d5016] mb-4">
              {getContentValue(content, 'overview', 'title', 'Economic Overview')}
            </h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              {getContentValue(content, 'overview', 'description', 'Economic description')}
            </p>

            {content?.overview?.stats && (
              <ul className="space-y-2 text-sm text-gray-700">
                {content.overview.stats.map((stat, index) => (
                  <li key={index}>
                    • <strong>{stat.label}:</strong> {stat.value}
                  </li>
                ))}
              </ul>
            )}

            {/* Investment Climate */}
            {content?.investment && (
              <>
                <h3 className="text-lg font-semibold text-[#2d5016] mt-6 mb-3">
                  {getContentValue(content, 'investment', 'title', 'Investment Climate')}
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  {getContentArray(content, 'investment', 'items', []).map((item, index) => (
                    <li key={index}>• {item}</li>
                  ))}
                </ul>
              </>
            )}
          </div>

          {/* Key Sectors */}
          {content?.sectors && (
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-[#2d5016] mb-4">
                <Building className="inline-block h-6 w-6 mr-2" />
                {getContentValue(content, 'sectors', 'title', 'Key Sectors')}
              </h2>
              <div className="space-y-4">
                {getContentArray(content, 'sectors', 'items', []).map((sector, index) => (
                  <div key={index} className="border-l-4 border-[#f4a261] pl-4">
                    <h4 className="font-semibold text-[#2d5016] mb-1">{sector.name}</h4>
                    <p className="text-sm text-gray-700">{sector.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Diaspora Opportunities */}
        {content?.diaspora && (
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-[#2d5016] mb-4">
              <Users className="inline-block h-6 w-6 mr-2" />
              {getContentValue(content, 'diaspora', 'title', 'Diaspora Opportunities')}
            </h2>
            <p className="text-gray-700 leading-relaxed mb-6">
              {getContentValue(content, 'diaspora', 'description', 'Diaspora description')}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {content.diaspora.incentives && (
                <div>
                  <h4 className="font-semibold text-[#2d5016] mb-3">Investment Incentives:</h4>
                  <ul className="space-y-2 text-sm text-gray-700">
                    {content.diaspora.incentives.map((incentive, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-[#f4a261] mr-2">•</span>
                        {incentive}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {content.diaspora.opportunities && (
                <div>
                  <h4 className="font-semibold text-[#2d5016] mb-3">Key Opportunities:</h4>
                  <ul className="space-y-2 text-sm text-gray-700">
                    {content.diaspora.opportunities.map((opportunity, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-[#f4a261] mr-2">•</span>
                        {opportunity}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-12 bg-[#2d5016] rounded-xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">
            Ready to Explore Opportunities in {getContentValue(content, 'hero', 'title', 'this country')}?
          </h3>
          <p className="text-lg mb-6 opacity-90">
            Get personalized guidance and expert support for your investment journey.
          </p>
          <Link
            to="/book-consultation"
            className="inline-flex items-center bg-[#f4a261] text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-[#e8956a] transition-colors duration-200"
          >
            Book a Consultation
            <ArrowLeft className="ml-2 h-5 w-5 rotate-180" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CountryPage;

import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, TrendingUp, Users, Building } from 'lucide-react';

const CountryProfiles = () => {
  const countries = [
    {
      name: 'Ivory Coast',
      flag: '/countries/ivory-coast.png',
      description: 'Ivory Coast is one of West Africa\'s fastest-growing economies and serves as a key economic hub for the Francophone region.',
      keyStats: {
        gdp: '$70.99B',
        population: '28.2M',
        growth: '6.2%',
        language: 'French'
      },
      sectors: ['Agriculture', 'Cocoa Production', 'Mining', 'Manufacturing'],
      businessClimate: 'Stable',
      slug: 'ivory-coast',
      highlights: [
        'World\'s largest cocoa producer',
        'Strategic location for West African trade',
        'Growing tech and fintech sector',
        'Strong infrastructure development'
      ]
    },
    {
      name: 'Senegal',
      flag: '/countries/senegal.png',
      description: 'Senegal has one of West Africa\'s most stable democracies and a steadily growing economy.',
      keyStats: {
        gdp: '$27.68B',
        population: '17.2M',
        growth: '4.8%',
        language: 'French'
      },
      sectors: ['Agriculture', 'Fishing', 'Mining', 'Tourism'],
      businessClimate: 'Very Stable',
      slug: 'senegal',
      highlights: [
        'Political stability and democratic governance',
        'Strategic Atlantic coast location',
        'Growing renewable energy sector',
        'Strong cultural and tourism industry'
      ]
    },
    {
      name: 'Morocco',
      flag: '/countries/morocco.png',
      description: 'Morocco boasts a stable and diverse economy with strong performance in agriculture, mining, manufacturing, and tourism.',
      keyStats: {
        gdp: '$134.18B',
        population: '37.5M',
        growth: '3.4%',
        language: 'Arabic/French'
      },
      sectors: ['Tourism', 'Agriculture', 'Mining', 'Manufacturing'],
      businessClimate: 'Stable',
      slug: 'morocco',
      highlights: [
        'Gateway to Africa and Europe',
        'World leader in phosphate production',
        'Growing automotive industry',
        'Strong renewable energy initiatives'
      ]
    },
    {
      name: 'Cameroon',
      flag: '/countries/cameroon.png',
      description: 'Cameroon has a mixed economy with strong agricultural, oil, and services sectors.',
      keyStats: {
        gdp: '$45.24B',
        population: '27.2M',
        growth: '3.7%',
        language: 'French/English'
      },
      sectors: ['Oil & Gas', 'Agriculture', 'Forestry', 'Mining'],
      businessClimate: 'Moderate',
      slug: 'cameroon',
      highlights: [
        'Central Africa\'s economic hub',
        'Rich natural resources',
        'Bilingual business environment',
        'Growing digital economy'
      ]
    },
    {
      name: 'Kenya',
      flag: '/countries/kenya.png',
      description: 'Kenya is East Africa\'s economic powerhouse with a diversified economy driven by services, agriculture, and industry.',
      keyStats: {
        gdp: '$115.49B',
        population: '54.0M',
        growth: '5.7%',
        language: 'English/Swahili'
      },
      sectors: ['Services', 'Agriculture', 'Manufacturing', 'Tourism'],
      businessClimate: 'Stable',
      slug: 'kenya',
      highlights: [
        'East Africa\'s financial hub',
        'Leading in mobile money innovation',
        'Strong tech ecosystem (Silicon Savannah)',
        'Gateway to East African markets'
      ]
    }
  ];

  return (
    <div className="py-16 bg-gray-200">
      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#2d5016] mb-6">
            Country Profiles
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Explore key African markets with detailed insights into business opportunities, 
            economic indicators, and investment potential across our featured countries.
          </p>
        </div>

        {/* Featured Markets Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-[#2d5016] text-center mb-12">
            Featured Markets
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {countries.map((country, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                {/* Country Header */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={country.flag}
                    alt={`${country.name} landscape`}
                    className="w-full h-full object-cover"
                  />
                  {/* Title overlay at bottom */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-2xl font-bold text-white">{country.name}</h3>
                      <div className={`px-3 py-1 rounded-full text-xs font-semibold ${
                        country.businessClimate === 'Very Stable' ? 'bg-green-500' :
                        country.businessClimate === 'Stable' ? 'bg-blue-500' : 'bg-yellow-500'
                      } text-white`}>
                        {country.businessClimate}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Country Content */}
                <div className="p-6">
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    {country.description}
                  </p>

                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-lg font-bold text-[#2d5016]">{country.keyStats.gdp}</div>
                      <div className="text-sm text-gray-600">GDP</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-[#2d5016]">{country.keyStats.population}</div>
                      <div className="text-sm text-gray-600">Population</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-[#2d5016]">{country.keyStats.growth}</div>
                      <div className="text-sm text-gray-600">GDP Growth</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-[#2d5016]">{country.keyStats.language}</div>
                      <div className="text-sm text-gray-600">Language</div>
                    </div>
                  </div>

                  {/* Key Sectors */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Sectors</h4>
                    <div className="flex flex-wrap gap-2">
                      {country.sectors.map((sector, sectorIndex) => (
                        <span
                          key={sectorIndex}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                        >
                          {sector}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Investment Highlights */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Investment Highlights</h4>
                    <ul className="space-y-2">
                      {country.highlights.map((highlight, highlightIndex) => (
                        <li key={highlightIndex} className="flex items-start">
                          <div className="w-2 h-2 bg-[#2d5016] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                          <span className="text-gray-700 text-sm">{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Explore Button */}
                  <Link
                    to={`/country-profiles/${country.slug}`}
                    className="w-full inline-flex items-center justify-center bg-[#2d5016] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#f4a261] transition-colors duration-200"
                  >
                    Explore More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Why Choose These Markets */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-[#2d5016] text-center mb-8">
            Why These Markets?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-[#2d5016] text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold text-[#2d5016] mb-3">
                Strong Growth Potential
              </h3>
              <p className="text-gray-700">
                These markets demonstrate consistent economic growth and expanding opportunities for diaspora investors.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#2d5016] text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <Building className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold text-[#2d5016] mb-3">
                Business-Friendly Environment
              </h3>
              <p className="text-gray-700">
                Established regulatory frameworks and government support for foreign investment and business development.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#2d5016] text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold text-[#2d5016] mb-3">
                Local Partnership Network
              </h3>
              <p className="text-gray-700">
                AfriPath has established trusted partnerships and local expertise in each of these key markets.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <h2 className="text-3xl font-bold text-[#2d5016] mb-6">
            Ready to Explore These Markets?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Get personalized insights and guidance for your target market.
          </p>
          <Link
            to="/book-consultation"
            className="inline-flex items-center bg-[#2d5016] text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-[#f4a261] transition-all duration-300 transform hover:scale-105"
          >
            Book Market Consultation
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>
    </div>
  );
};

export default CountryProfiles;

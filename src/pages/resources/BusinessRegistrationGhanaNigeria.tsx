import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Calendar, CheckCircle, Clock, DollarSign, FileText, AlertCircle } from 'lucide-react';

const BusinessRegistrationGhanaNigeria = () => {
  const ghanaSteps = [
    {
      step: 1,
      title: 'Name Reservation',
      duration: '1-2 days',
      cost: '$15-25',
      description: 'Reserve your company name with the Registrar General\'s Department',
      requirements: ['Proposed company name', 'Alternative names (2-3)', 'Applicant details']
    },
    {
      step: 2,
      title: 'Company Registration',
      duration: '3-5 days',
      cost: '$200-400',
      description: 'Submit incorporation documents and pay registration fees',
      requirements: ['Memorandum & Articles of Association', 'Form 1 & 2', 'Statutory declaration']
    },
    {
      step: 3,
      title: 'Tax Registration',
      duration: '2-3 days',
      cost: '$50-100',
      description: 'Register with Ghana Revenue Authority for TIN',
      requirements: ['Certificate of incorporation', 'Business address proof', 'Director details']
    },
    {
      step: 4,
      title: 'Business License',
      duration: '5-10 days',
      cost: '$100-500',
      description: 'Obtain relevant business operating license',
      requirements: ['Industry-specific requirements', 'Location permit', 'Environmental clearance (if applicable)']
    }
  ];

  const nigeriaSteps = [
    {
      step: 1,
      title: 'Name Reservation',
      duration: '1-3 days',
      cost: '$20-30',
      description: 'Reserve company name with Corporate Affairs Commission (CAC)',
      requirements: ['Proposed company name', 'Alternative names', 'Applicant identification']
    },
    {
      step: 2,
      title: 'Company Registration',
      duration: '5-7 days',
      cost: '$300-600',
      description: 'Complete CAC registration process',
      requirements: ['Memorandum & Articles of Association', 'CAC Forms', 'Statutory declaration', 'Consent letters from directors']
    },
    {
      step: 3,
      title: 'Tax Registration',
      duration: '3-5 days',
      cost: '$100-200',
      description: 'Register with Federal Inland Revenue Service (FIRS)',
      requirements: ['Certificate of incorporation', 'Tax identification number application', 'Business address']
    },
    {
      step: 4,
      title: 'Business Permits',
      duration: '7-14 days',
      cost: '$200-800',
      description: 'Obtain necessary business permits and licenses',
      requirements: ['Industry-specific licenses', 'State permits', 'Local government permits']
    }
  ];

  const comparisonData = [
    { aspect: 'Total Timeline', ghana: '11-20 days', nigeria: '16-29 days' },
    { aspect: 'Total Cost', ghana: '$365-1,025', nigeria: '$620-1,630' },
    { aspect: 'Minimum Capital', ghana: 'No minimum', nigeria: '₦100,000 (~$65)' },
    { aspect: 'Foreign Ownership', ghana: '100% allowed', nigeria: '100% allowed (most sectors)' },
    { aspect: 'Online Process', ghana: 'Partially available', nigeria: 'Fully online available' },
    { aspect: 'Language', ghana: 'English', nigeria: 'English' }
  ];

  return (
    <div className="min-h-screen bg-gray-200">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <Link
            to="/resources"
            className="inline-flex items-center text-[#2d5016] hover:text-[#f4a261] transition-colors duration-200 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Resources
          </Link>
          
          <div className="flex items-center text-sm text-gray-500 mb-4">
            <Calendar className="h-4 w-4 mr-2" />
            <span>January 10, 2025</span>
            <span className="mx-2">•</span>
            <span>12 min read</span>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
            How to Legally Register a Business in Ghana or Nigeria
          </h1>
          
          <p className="text-xl text-gray-600 leading-relaxed">
            Step-by-step guide to business registration processes, required documentation, timelines, 
            and costs for two of Africa's most popular investment destinations.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Introduction */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Why Ghana and Nigeria?
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Ghana and Nigeria represent two of the most attractive destinations for diaspora business 
            investment in West Africa. Both countries offer English-speaking business environments, 
            growing economies, and established legal frameworks for foreign investment. This comprehensive 
            guide walks you through the complete business registration process for both countries.
          </p>
          
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
              <div>
                <p className="text-blue-800 font-medium">Important Note</p>
                <p className="text-blue-700 text-sm">
                  While this guide provides comprehensive information, we recommend consulting with 
                  local legal experts or our professional services for personalized guidance.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Comparison */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Ghana vs Nigeria: Quick Comparison
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-[#2d5016]">Aspect</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#2d5016]">Ghana</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#2d5016]">Nigeria</th>
                </tr>
              </thead>
              <tbody>
                {comparisonData.map((item, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-3 px-4 font-medium text-gray-700">{item.aspect}</td>
                    <td className="py-3 px-4 text-gray-600">{item.ghana}</td>
                    <td className="py-3 px-4 text-gray-600">{item.nigeria}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Ghana Registration Process */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Ghana Business Registration Process
          </h2>
          <div className="space-y-6">
            {ghanaSteps.map((step, index) => (
              <div key={index} className="border-l-4 border-[#2d5016] pl-6 pb-6">
                <div className="flex items-center mb-3">
                  <div className="bg-[#2d5016] text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">
                    {step.step}
                  </div>
                  <h3 className="text-xl font-semibold text-[#2d5016]">{step.title}</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">{step.duration}</span>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">{step.cost}</span>
                  </div>
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">{step.requirements.length} documents</span>
                  </div>
                </div>
                
                <p className="text-gray-700 mb-3">{step.description}</p>
                
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Required Documents:</h4>
                  <ul className="space-y-1">
                    {step.requirements.map((req, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Nigeria Registration Process */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Nigeria Business Registration Process
          </h2>
          <div className="space-y-6">
            {nigeriaSteps.map((step, index) => (
              <div key={index} className="border-l-4 border-[#2d5016] pl-6 pb-6">
                <div className="flex items-center mb-3">
                  <div className="bg-[#2d5016] text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">
                    {step.step}
                  </div>
                  <h3 className="text-xl font-semibold text-[#2d5016]">{step.title}</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">{step.duration}</span>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">{step.cost}</span>
                  </div>
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">{step.requirements.length} documents</span>
                  </div>
                </div>
                
                <p className="text-gray-700 mb-3">{step.description}</p>
                
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Required Documents:</h4>
                  <ul className="space-y-1">
                    {step.requirements.map((req, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-[#2d5016] text-white rounded-xl p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">Need Professional Assistance?</h3>
          <p className="text-gray-200 mb-6">
            Our expert team can handle the entire business registration process for you, ensuring 
            compliance and saving you time and effort.
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-[#f4a261] text-[#2d5016] px-6 py-3 rounded-lg font-semibold hover:bg-[#e76f51] transition-colors duration-200"
          >
            Get Professional Help
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BusinessRegistrationGhanaNigeria;

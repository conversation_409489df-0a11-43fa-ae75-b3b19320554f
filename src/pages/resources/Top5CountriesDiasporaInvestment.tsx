import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Calendar, MapPin, TrendingUp, DollarSign, Users, Building } from 'lucide-react';

const Top5CountriesDiasporaInvestment = () => {
  const countries = [
    {
      name: 'Ghana',
      rank: 1,
      gdpGrowth: '5.4%',
      easeOfBusiness: '118/190',
      keyIndustries: ['Technology', 'Agriculture', 'Manufacturing', 'Tourism'],
      diasporaPrograms: 'Year of Return, Beyond the Return',
      investment: '$2.5B+ diaspora investment',
      highlights: [
        'Stable democratic government',
        'English-speaking business environment',
        'Strong diaspora engagement policies',
        'Growing tech ecosystem in Accra'
      ]
    },
    {
      name: 'Nigeria',
      rank: 2,
      gdpGrowth: '3.2%',
      easeOfBusiness: '131/190',
      keyIndustries: ['Oil & Gas', 'Fintech', 'Agriculture', 'Entertainment'],
      diasporaPrograms: 'Nigerians in Diaspora Commission (NIDCOM)',
      investment: '$25B+ annual diaspora remittances',
      highlights: [
        'Largest economy in Africa',
        'Thriving fintech and startup ecosystem',
        'Large domestic market (200M+ population)',
        'Strong cultural ties with diaspora'
      ]
    },
    {
      name: 'Kenya',
      rank: 3,
      gdpGrowth: '5.7%',
      easeOfBusiness: '56/190',
      keyIndustries: ['Technology', 'Agriculture', 'Tourism', 'Manufacturing'],
      diasporaPrograms: 'Kenya Diaspora Remittances Survey',
      investment: '$3.7B+ diaspora remittances',
      highlights: [
        'Regional hub for East Africa',
        'Advanced mobile money ecosystem',
        'Strong regulatory framework',
        'Growing renewable energy sector'
      ]
    },
    {
      name: 'Rwanda',
      rank: 4,
      gdpGrowth: '8.2%',
      easeOfBusiness: '38/190',
      keyIndustries: ['Technology', 'Tourism', 'Agriculture', 'Manufacturing'],
      diasporaPrograms: 'Rwanda Diaspora Global Network',
      investment: '$400M+ diaspora investment',
      highlights: [
        'Excellent ease of doing business',
        'Strong governance and low corruption',
        'Vision 2050 development strategy',
        'Growing as regional tech hub'
      ]
    },
    {
      name: 'South Africa',
      rank: 5,
      gdpGrowth: '1.9%',
      easeOfBusiness: '84/190',
      keyIndustries: ['Mining', 'Financial Services', 'Manufacturing', 'Technology'],
      diasporaPrograms: 'South African Diaspora Network',
      investment: '$1.2B+ diaspora investment',
      highlights: [
        'Most developed financial markets',
        'Strong infrastructure',
        'Gateway to Southern Africa',
        'Established business ecosystem'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-200">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <Link
            to="/resources"
            className="inline-flex items-center text-[#2d5016] hover:text-[#f4a261] transition-colors duration-200 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Resources
          </Link>
          
          <div className="flex items-center text-sm text-gray-500 mb-4">
            <Calendar className="h-4 w-4 mr-2" />
            <span>January 15, 2025</span>
            <span className="mx-2">•</span>
            <span>8 min read</span>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
            Top 5 Countries for Diaspora Investment in 2025
          </h1>
          
          <p className="text-xl text-gray-600 leading-relaxed">
            Explore the most promising African markets for diaspora entrepreneurs, featuring comprehensive 
            analysis of business climate, regulatory environment, and growth opportunities.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Executive Summary
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            As we enter 2025, African diaspora investment continues to grow, with over $95 billion in 
            remittances flowing to the continent annually. This comprehensive analysis examines the top 
            five countries offering the most attractive opportunities for diaspora entrepreneurs and 
            investors, based on economic stability, business environment, growth potential, and 
            diaspora-friendly policies.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <TrendingUp className="h-8 w-8 text-[#2d5016] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#2d5016]">$95B+</div>
              <div className="text-sm text-gray-600">Annual Diaspora Remittances</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Users className="h-8 w-8 text-[#2d5016] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#2d5016]">140M+</div>
              <div className="text-sm text-gray-600">African Diaspora Worldwide</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Building className="h-8 w-8 text-[#2d5016] mx-auto mb-2" />
              <div className="text-2xl font-bold text-[#2d5016]">54</div>
              <div className="text-sm text-gray-600">Countries Analyzed</div>
            </div>
          </div>
        </div>

        {/* Country Rankings */}
        <div className="space-y-8">
          {countries.map((country, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center mb-6">
                <div className="bg-[#2d5016] text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold mr-4">
                  {country.rank}
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-[#2d5016]">{country.name}</h3>
                  <p className="text-gray-600">{country.diasporaPrograms}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">GDP Growth</div>
                  <div className="text-lg font-semibold text-[#2d5016]">{country.gdpGrowth}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Ease of Business</div>
                  <div className="text-lg font-semibold text-[#2d5016]">{country.easeOfBusiness}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Diaspora Investment</div>
                  <div className="text-lg font-semibold text-[#2d5016]">{country.investment}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Key Industries</div>
                  <div className="text-sm text-[#2d5016]">{country.keyIndustries.slice(0, 2).join(', ')}</div>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="font-semibold text-[#2d5016] mb-2">Key Highlights:</h4>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {country.highlights.map((highlight, idx) => (
                    <li key={idx} className="flex items-center text-gray-700">
                      <div className="w-2 h-2 bg-[#2d5016] rounded-full mr-3"></div>
                      {highlight}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="bg-[#2d5016] text-white rounded-xl p-8 mt-12 text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Explore Investment Opportunities?</h3>
          <p className="text-gray-200 mb-6">
            Get personalized guidance on investing in these top African markets with our expert consultation services.
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-[#f4a261] text-[#2d5016] px-6 py-3 rounded-lg font-semibold hover:bg-[#e76f51] transition-colors duration-200"
          >
            Schedule Consultation
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Top5CountriesDiasporaInvestment;

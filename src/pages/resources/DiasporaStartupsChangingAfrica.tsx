import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Calendar, TrendingUp, Users, Globe, Award, ExternalLink } from 'lucide-react';

const DiasporaStartupsChangingAfrica = () => {
  const startups = [
    {
      name: 'Flutterwave',
      founder: '<PERSON>lugben<PERSON>la',
      country: 'Nigeria',
      industry: 'Fintech',
      founded: '2016',
      valuation: '$3B+',
      impact: 'Processing $16B+ in transactions annually',
      description: 'Payment infrastructure for global merchants and payment service providers',
      achievements: [
        'Unicorn status achieved in 2021',
        'Serves 900,000+ businesses',
        'Operates in 34+ African countries',
        'Raised $250M+ in funding'
      ]
    },
    {
      name: '<PERSON><PERSON>',
      founder: '<PERSON> (Co-founder)',
      country: 'Nigeria/Kenya/Uganda',
      industry: 'Technology/Education',
      founded: '2014',
      valuation: '$1.5B+',
      impact: 'Trained 100,000+ developers across Africa',
      description: 'Global talent network connecting African developers with international companies',
      achievements: [
        'Unicorn status achieved in 2021',
        'Partners with 200+ global companies',
        'Operates across 6 African countries',
        'Raised $400M+ in total funding'
      ]
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      founder: '<PERSON>',
      country: 'Rwanda/Ghana',
      industry: 'Healthcare/Logistics',
      founded: '2014',
      valuation: '$2.75B',
      impact: 'Delivered 500,000+ medical products via drone',
      description: 'Autonomous drone delivery service for medical supplies and blood products',
      achievements: [
        'First commercial drone delivery service',
        'Serves 2,500+ health facilities',
        'Reduced delivery time from hours to minutes',
        'Expanded to multiple countries'
      ]
    },
    {
      name: 'Kobo360',
      founder: 'Obi Ozor',
      country: 'Nigeria',
      industry: 'Logistics',
      founded: '2017',
      valuation: '$150M+',
      impact: 'Moved 1M+ tons of cargo across Africa',
      description: 'Digital freight platform connecting cargo owners with truck drivers',
      achievements: [
        'Operates in 10+ African countries',
        'Network of 10,000+ drivers',
        'Raised $50M+ in funding',
        'Reduced logistics costs by 40%'
      ]
    },
    {
      name: 'Paystack',
      founder: 'Shola Akinlade & Ezra Olubi',
      country: 'Nigeria',
      industry: 'Fintech',
      founded: '2015',
      valuation: '$200M (acquired by Stripe)',
      impact: 'Processed $10B+ in transactions',
      description: 'Payment processing platform for African businesses',
      achievements: [
        'Acquired by Stripe for $200M',
        'Serves 60,000+ businesses',
        'Processes millions of transactions monthly',
        'Expanded across West Africa'
      ]
    },
    {
      name: 'Twiga Foods',
      founder: 'Peter Njonjo',
      country: 'Kenya',
      industry: 'AgriTech',
      founded: '2013',
      valuation: '$87M+',
      impact: 'Serves 35,000+ vendors and 13,000+ farmers',
      description: 'B2B marketplace connecting farmers to urban retailers',
      achievements: [
        'Largest B2B food platform in Africa',
        'Reduced food waste by 30%',
        'Raised $50M+ in funding',
        'Expanded to Uganda and Tanzania'
      ]
    }
  ];

  const impactStats = [
    { metric: '$50B+', description: 'Combined valuation of diaspora-led startups' },
    { metric: '2M+', description: 'Jobs created across the continent' },
    { metric: '100M+', description: 'People impacted by these innovations' },
    { metric: '54', description: 'Countries with diaspora-led startups' }
  ];

  const sectors = [
    { name: 'Fintech', percentage: 35, companies: 'Flutterwave, Paystack, Chipper Cash' },
    { name: 'E-commerce', percentage: 20, companies: 'Jumia, Konga, Mall for Africa' },
    { name: 'Healthcare', percentage: 15, companies: 'Zipline, 54gene, mPharma' },
    { name: 'Agriculture', percentage: 12, companies: 'Twiga Foods, Farmcrowdy, AgroStar' },
    { name: 'Education', percentage: 10, companies: 'Andela, uLesson, Gradely' },
    { name: 'Logistics', percentage: 8, companies: 'Kobo360, Lori Systems, Sendy' }
  ];

  return (
    <div className="min-h-screen bg-gray-200">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <Link
            to="/resources"
            className="inline-flex items-center text-[#2d5016] hover:text-[#f4a261] transition-colors duration-200 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Resources
          </Link>
          
          <div className="flex items-center text-sm text-gray-500 mb-4">
            <Calendar className="h-4 w-4 mr-2" />
            <span>January 5, 2025</span>
            <span className="mx-2">•</span>
            <span>10 min read</span>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
            Diaspora-led Startups Changing Africa
          </h1>
          
          <p className="text-xl text-gray-600 leading-relaxed">
            Success stories and case studies of innovative diaspora entrepreneurs who are making 
            significant impact across various African industries and markets.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Introduction */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            The Diaspora Innovation Revolution
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            African diaspora entrepreneurs are at the forefront of the continent's technological 
            revolution. Leveraging their global experience, international networks, and deep 
            understanding of local challenges, these innovators are building solutions that 
            address Africa's most pressing needs while creating significant economic value.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {impactStats.map((stat, index) => (
              <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-[#2d5016] mb-2">{stat.metric}</div>
                <div className="text-sm text-gray-600">{stat.description}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Sector Distribution */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Industry Distribution
          </h2>
          <div className="space-y-4">
            {sectors.map((sector, index) => (
              <div key={index} className="flex items-center">
                <div className="w-24 text-sm font-medium text-gray-700">{sector.name}</div>
                <div className="flex-1 mx-4">
                  <div className="bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-[#2d5016] h-3 rounded-full" 
                      style={{ width: `${sector.percentage}%` }}
                    ></div>
                  </div>
                </div>
                <div className="w-12 text-sm text-gray-600">{sector.percentage}%</div>
              </div>
            ))}
          </div>
        </div>

        {/* Success Stories */}
        <div className="space-y-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Success Stories
          </h2>
          
          {startups.map((startup, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-6">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-2xl font-bold text-[#2d5016] mb-2">{startup.name}</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                    <span className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {startup.founder}
                    </span>
                    <span className="flex items-center">
                      <Globe className="h-4 w-4 mr-1" />
                      {startup.country}
                    </span>
                    <span className="flex items-center">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      {startup.industry}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-[#2d5016]">{startup.valuation}</div>
                  <div className="text-sm text-gray-600">Valuation</div>
                </div>
              </div>

              <p className="text-gray-700 mb-4">{startup.description}</p>
              
              <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
                <div className="flex items-center mb-2">
                  <Award className="h-5 w-5 text-green-600 mr-2" />
                  <span className="font-medium text-green-800">Impact</span>
                </div>
                <p className="text-green-700">{startup.impact}</p>
              </div>

              <div>
                <h4 className="font-semibold text-[#2d5016] mb-3">Key Achievements:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {startup.achievements.map((achievement, idx) => (
                    <div key={idx} className="flex items-center text-gray-700">
                      <div className="w-2 h-2 bg-[#2d5016] rounded-full mr-3"></div>
                      <span className="text-sm">{achievement}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Key Success Factors */}
        <div className="bg-white rounded-xl shadow-lg p-8 mt-8">
          <h2 className="text-2xl font-bold text-[#2d5016] mb-6">
            Key Success Factors
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-[#2d5016] mb-3">Global Perspective</h3>
              <p className="text-gray-700 text-sm">
                Diaspora entrepreneurs bring international best practices and global market 
                understanding to local challenges.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-[#2d5016] mb-3">Access to Capital</h3>
              <p className="text-gray-700 text-sm">
                Strong networks in international markets provide access to venture capital 
                and growth funding.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-[#2d5016] mb-3">Cultural Understanding</h3>
              <p className="text-gray-700 text-sm">
                Deep knowledge of local markets, customs, and consumer behavior drives 
                product-market fit.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-[#2d5016] mb-3">Technology Transfer</h3>
              <p className="text-gray-700 text-sm">
                Ability to adapt and implement proven technologies from developed markets 
                to African contexts.
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-[#2d5016] text-white rounded-xl p-8 mt-8 text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Build Your African Success Story?</h3>
          <p className="text-gray-200 mb-6">
            Join the ranks of successful diaspora entrepreneurs making a difference across Africa. 
            Get expert guidance to turn your vision into reality.
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-[#f4a261] text-[#2d5016] px-6 py-3 rounded-lg font-semibold hover:bg-[#e76f51] transition-colors duration-200"
          >
            Start Your Journey
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DiasporaStartupsChangingAfrica;

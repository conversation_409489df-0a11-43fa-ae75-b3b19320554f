import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, ArrowRight, Star } from 'lucide-react';

const Resources = () => {
  const blogPosts = [
    {
      title: 'Top 5 Countries for Diaspora Investment in 2025',
      intro: 'Explore the most promising African markets for diaspora entrepreneurs, featuring comprehensive analysis of business climate, regulatory environment, and growth opportunities.',
      image: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg',
      date: 'January 15, 2025',
      readTime: '8 min read',
      link: '/resources/top-5-countries-diaspora-investment-2025'
    },
    {
      title: 'How to Legally Register a Business in Ghana or Nigeria',
      intro: 'Step-by-step guide to business registration processes, required documentation, timelines, and costs for two of Africa\'s most popular investment destinations.',
      image: 'https://images.pexels.com/photos/3184405/pexels-photo-3184405.jpeg',
      date: 'January 10, 2025',
      readTime: '12 min read',
      link: '/resources/business-registration-ghana-nigeria'
    },
    {
      title: 'Diaspora-led Startups Changing Africa',
      intro: 'Success stories and case studies of innovative diaspora entrepreneurs who are making significant impact across various African industries and markets.',
      image: 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg',
      date: 'January 5, 2025',
      readTime: '10 min read',
      link: '/resources/diaspora-startups-changing-africa'
    }
  ];

  const testimonials = [
    {
      name: 'Kwame Asante',
      location: 'Toronto, Canada',
      image: 'https://images.pexels.com/photos/2381069/pexels-photo-2381069.jpeg',
      story: 'Afripath helped me expand my tech consultancy into Senegal within 6 months. Their local partnerships and cultural expertise made the difference between success and failure. The regulatory guidance was invaluable.',
      business: 'Tech Consultancy',
      rating: 5
    },
    {
      name: 'Fatou Diallo',
      location: 'Paris, France',
      image: 'https://images.pexels.com/photos/2381122/pexels-photo-2381122.jpeg',
      story: 'Their strategy support was key in forming local partnerships for my sustainable fashion brand. Afripath understood both my vision and the local market dynamics perfectly.',
      business: 'Sustainable Fashion',
      rating: 5
    },
    {
      name: 'Michael Okafor',
      location: 'London, UK',
      image: 'https://images.pexels.com/photos/2381087/pexels-photo-2381087.jpeg',
      story: 'The trade visit program opened doors I never knew existed. Meeting potential partners face-to-face in Lagos transformed my agriculture import business completely.',
      business: 'Agricultural Imports',
      rating: 5
    },
    {
      name: 'Amara Camara',
      location: 'Washington, DC',
      image: 'https://images.pexels.com/photos/2381092/pexels-photo-2381092.jpeg',
      story: 'From initial consultation to business launch, Afripath provided comprehensive support. Their legal team handled all compliance issues while I focused on building my customer base.',
      business: 'Financial Services',
      rating: 5
    }
  ];

  return (
    <div className="py-16 bg-gray-200">
      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#2d5016] mb-6">
            Resources & Success Stories
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Insights, guides, and real stories from diaspora entrepreneurs 
            building successful businesses across Africa.
          </p>
        </div>
      </section>

      {/* Blog Posts Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-20">
        <div className="mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
            Latest Insights
          </h2>
          <p className="text-xl text-gray-600">
            Expert guidance and market analysis for African business success
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <article
              key={index}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span>{post.readTime}</span>
                </div>
                <h3 className="text-xl font-semibold text-[#2d5016] mb-3 leading-tight">
                  {post.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {post.intro}
                </p>
                <Link
                  to={post.link}
                  className="inline-flex items-center text-[#2d5016] font-semibold hover:text-[#f4a261] transition-colors duration-200"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </article>
          ))}
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="bg-gray-200 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
              Client Success Stories
            </h2>
            <p className="text-xl text-gray-600">
              Real entrepreneurs, real results, real impact across Africa
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex items-center mb-6">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h3 className="font-semibold text-[#2d5016] text-lg">
                      {testimonial.name}
                    </h3>
                    <p className="text-gray-600">{testimonial.location}</p>
                    <p className="text-sm text-[#2d5016]">{testimonial.business}</p>
                  </div>
                </div>

                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>

                <p className="text-gray-700 leading-relaxed italic">
                  "{testimonial.story}"
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter CTA */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-6">
            Stay Updated
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Get the latest insights, market updates, and success stories delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
            />
            <button className="bg-[#2d5016] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#1a2e1a] transition-colors duration-200">
              Subscribe
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Resources;
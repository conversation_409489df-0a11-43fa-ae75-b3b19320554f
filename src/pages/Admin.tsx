import React, { useState, useEffect } from 'react';
import { Save, LogOut, Eye, Edit3, AlertCircle, CheckCircle } from 'lucide-react';

// Configuration de l'API
const API_BASE_URL = 'https://afripath-cms-api.badoo-7.workers.dev';
const AUTH_TOKEN = 'admin-token-2025'; // À sécuriser en production

interface ContentBlock {
  [key: string]: any;
}

interface PageContent {
  [blockId: string]: ContentBlock;
}

interface SiteContent {
  [page: string]: PageContent;
}

const Admin = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [content, setContent] = useState<SiteContent>({});
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [selectedPage, setSelectedPage] = useState('home');

  // Authentification simple (à améliorer en production)
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (password === 'admin2025') { // À sécuriser !
      setIsAuthenticated(true);
      loadContent();
    } else {
      setMessage({ type: 'error', text: 'Mot de passe incorrect' });
    }
  };

  // Charger le contenu depuis l'API
  const loadContent = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/content`);
      if (response.ok) {
        const data = await response.json();
        setContent(data.pages || {});
      } else {
        setMessage({ type: 'error', text: 'Erreur lors du chargement du contenu' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur de connexion à l\'API' });
    } finally {
      setLoading(false);
    }
  };

  // Sauvegarder un bloc de contenu
  const saveBlock = async (page: string, blockId: string, blockContent: ContentBlock) => {
    setSaving(true);
    try {
      const response = await fetch(`${API_BASE_URL}/content/${page}/${blockId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AUTH_TOKEN}`,
        },
        body: JSON.stringify(blockContent),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: `Bloc ${blockId} sauvegardé avec succès` });
        // Mettre à jour le contenu local
        setContent(prev => ({
          ...prev,
          [page]: {
            ...prev[page],
            [blockId]: blockContent
          }
        }));
      } else {
        setMessage({ type: 'error', text: 'Erreur lors de la sauvegarde' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur de connexion' });
    } finally {
      setSaving(false);
    }
  };

  // Gérer les modifications de contenu
  const handleContentChange = (page: string, blockId: string, field: string, value: string) => {
    setContent(prev => ({
      ...prev,
      [page]: {
        ...prev[page],
        [blockId]: {
          ...prev[page]?.[blockId],
          [field]: value
        }
      }
    }));
  };

  // Effacer les messages après 3 secondes
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  // Interface de connexion
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <h1 className="text-2xl font-bold text-[#2d5016] mb-6 text-center">
            Administration AfriPath
          </h1>
          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Mot de passe
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#f4a261]"
                placeholder="Entrez le mot de passe admin"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full bg-[#2d5016] text-white py-2 px-4 rounded-lg hover:bg-[#f4a261] transition-colors"
            >
              Se connecter
            </button>
          </form>
          {message && (
            <div className={`mt-4 p-3 rounded-lg ${message.type === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
              {message.text}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Interface d'administration
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header Admin */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-bold text-[#2d5016]">
              Administration AfriPath CMS
            </h1>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setEditMode(!editMode)}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  editMode 
                    ? 'bg-[#f4a261] text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {editMode ? <Eye className="h-4 w-4 mr-2" /> : <Edit3 className="h-4 w-4 mr-2" />}
                {editMode ? 'Mode Aperçu' : 'Mode Édition'}
              </button>
              <button
                onClick={() => setIsAuthenticated(false)}
                className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Déconnexion
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Messages */}
      {message && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <div className={`flex items-center p-4 rounded-lg ${
            message.type === 'error' 
              ? 'bg-red-100 text-red-700' 
              : 'bg-green-100 text-green-700'
          }`}>
            {message.type === 'error' ? (
              <AlertCircle className="h-5 w-5 mr-2" />
            ) : (
              <CheckCircle className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </div>
      )}

      {/* Navigation des pages */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-wrap gap-2 mb-6">
            {Object.keys(content).map((page) => {
              const pageLabels: { [key: string]: string } = {
                'home': 'Accueil',
                'about': 'À propos',
                'services': 'Services',
                'kenya': 'Kenya',
                'cameroon': 'Cameroun',
                'senegal': 'Sénégal',
                'ivorycoast': 'Côte d\'Ivoire',
                'morocco': 'Maroc'
              };

              return (
                <button
                  key={page}
                  onClick={() => setSelectedPage(page)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    selectedPage === page
                      ? 'bg-[#2d5016] text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {pageLabels[page] || page.charAt(0).toUpperCase() + page.slice(1)}
                </button>
              );
            })}
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2d5016] mx-auto"></div>
              <p className="mt-2 text-gray-600">Chargement du contenu...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {content[selectedPage] && Object.entries(content[selectedPage]).map(([blockId, blockContent]) => (
                <ContentBlockEditor
                  key={blockId}
                  page={selectedPage}
                  blockId={blockId}
                  blockContent={blockContent}
                  editMode={editMode}
                  onContentChange={handleContentChange}
                  onSave={saveBlock}
                  saving={saving}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Composant pour éditer un bloc de contenu
interface ContentBlockEditorProps {
  page: string;
  blockId: string;
  blockContent: ContentBlock;
  editMode: boolean;
  onContentChange: (page: string, blockId: string, field: string, value: string) => void;
  onSave: (page: string, blockId: string, content: ContentBlock) => void;
  saving: boolean;
}

const ContentBlockEditor: React.FC<ContentBlockEditorProps> = ({
  page,
  blockId,
  blockContent,
  editMode,
  onContentChange,
  onSave,
  saving
}) => {
  const renderField = (key: string, value: any) => {
    if (typeof value === 'string') {
      return (
        <div key={key} className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {key.charAt(0).toUpperCase() + key.slice(1)}
          </label>
          {editMode ? (
            <textarea
              value={value}
              onChange={(e) => onContentChange(page, blockId, key, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#f4a261] resize-vertical"
              rows={value.length > 100 ? 4 : 2}
            />
          ) : (
            <div className="p-3 bg-gray-50 rounded-lg">
              {value}
            </div>
          )}
        </div>
      );
    } else if (Array.isArray(value)) {
      return (
        <div key={key} className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {key.charAt(0).toUpperCase() + key.slice(1)}
          </label>
          <div className="space-y-2">
            {value.map((item, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                {typeof item === 'object' ? JSON.stringify(item, null, 2) : item}
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="border border-gray-200 rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-[#2d5016]">
          {blockId.charAt(0).toUpperCase() + blockId.slice(1)}
        </h3>
        {editMode && (
          <button
            onClick={() => onSave(page, blockId, blockContent)}
            disabled={saving}
            className="flex items-center px-4 py-2 bg-[#2d5016] text-white rounded-lg hover:bg-[#f4a261] transition-colors disabled:opacity-50"
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Sauvegarde...' : 'Sauvegarder'}
          </button>
        )}
      </div>
      
      <div className="space-y-4">
        {Object.entries(blockContent).map(([key, value]) => renderField(key, value))}
      </div>
    </div>
  );
};

export default Admin;

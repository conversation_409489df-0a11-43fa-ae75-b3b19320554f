import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, User, Mail, Phone, MapPin, Send, CheckCircle, AlertCircle } from 'lucide-react';
import emailjs from '@emailjs/browser';
import ReCA<PERSON>TCHA from 'react-google-recaptcha';
import { EMAILJS_CONFIG, ConsultationFormData, EmailTemplateParams } from '../config/emailjs';
import { RECAPTCHA_CONFIG } from '../config/recaptcha';

const BookConsultation = () => {
  const navigate = useNavigate();
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    location: '',
    consultationDate: '',
    serviceInterest: '',
    message: ''
  });
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  // Get today's date in YYYY-MM-DD format for min date
  const today = new Date().toISOString().split('T')[0];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Fonction pour gérer le changement du reCAPTCHA
  const handleRecaptchaChange = (token: string | null) => {
    setRecaptchaToken(token);
    if (submitError && token) {
      setSubmitError(null); // Clear error when recaptcha is completed
    }
  };

  // Fonction pour gérer l'expiration du reCAPTCHA
  const handleRecaptchaExpired = () => {
    setRecaptchaToken(null);
  };

  // Fonction pour envoyer l'email via EmailJS
  const sendEmail = async (formData: any, recaptchaToken: string) => {
    try {
      // Préparer les paramètres du template selon la documentation EmailJS
      const templateParams = {
        user_name: formData.fullName,
        user_email: formData.email,
        user_phone: formData.phone,
        user_location: formData.location,
        service_interest: formData.serviceInterest,
        consultation_date: formData.consultationDate,
        message: formData.message,
        to_email: EMAILJS_CONFIG.TO_EMAIL,
        'g-recaptcha-response': recaptchaToken
      };

      // Envoyer l'email selon la documentation officielle
      const response = await emailjs.send(
        EMAILJS_CONFIG.SERVICE_ID,
        EMAILJS_CONFIG.TEMPLATE_ID,
        templateParams,
        {
          publicKey: EMAILJS_CONFIG.PUBLIC_KEY,
        }
      );

      console.log('Email envoyé avec succès:', response.status, response.text);
      return { success: true, response };
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email:', error);
      return { success: false, error };
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🚀 handleSubmit appelé !', formData);
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError(null);

    // Vérifier que le reCAPTCHA est complété
    if (!recaptchaToken) {
      setSubmitError('Veuillez compléter la vérification reCAPTCHA.');
      setIsSubmitting(false);
      return;
    }

    try {
      // Envoyer l'email via EmailJS
      const emailResult = await sendEmail(formData);

      if (!emailResult.success) {
        throw new Error('Erreur lors de l\'envoi de l\'email');
      }

      // Check if service is free or paid
      const freeServices = [
        'Discovery (Free)'
      ];

      const isPaidService = !freeServices.includes(formData.serviceInterest);

      if (isPaidService) {
        // Redirect to payment page for paid services
        navigate('/payment', {
          state: {
            consultationData: formData
          }
        });
      } else {
        // For free services, show success message and reset form
        console.log('Free consultation booked:', formData);
        setShowSuccessMessage(true);
        // Reset form
        setFormData({
          fullName: '',
          email: '',
          phone: '',
          location: '',
          consultationDate: '',
          serviceInterest: '',
          message: ''
        });
        // Reset reCAPTCHA
        setRecaptchaToken(null);
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
        }
        // Hide success message after 5 seconds
        setTimeout(() => {
          setShowSuccessMessage(false);
        }, 5000);
      }
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
      setSubmitError('Une erreur est survenue lors de l\'envoi de votre demande. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const services = [
    'Discovery (Free)',
    'Consultation (Paid)'
  ];

  return (
    <div className="py-16 bg-gray-200">
      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#2d5016] mb-6">
            Book Your Consultation
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Ready to turn your African business dreams into reality? 
            Schedule your consultation to discuss your goals and next steps.
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Modal */}
        {showSuccessMessage && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Background overlay */}
            <div
              className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-fade-in"
              onClick={() => setShowSuccessMessage(false)}
            ></div>

            {/* Modal content */}
            <div className="relative bg-white rounded-2xl shadow-2xl max-w-md mx-4 p-8 animate-scale-in">
              <div className="text-center">
                {/* Success icon */}
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                  <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-[#2d5016] mb-4">
                  Booking Confirmed!
                </h3>

                {/* Message */}
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Merci pour votre demande de consultation ! Nous avons bien reçu votre message et vous contacterons dans les 24 heures pour confirmer votre rendez-vous.
                </p>
                <p className="text-sm text-gray-500 mb-6">
                  Un email de confirmation a été envoyé à <strong>{formData.email}</strong>
                </p>

                {/* Close button */}
                <button
                  onClick={() => setShowSuccessMessage(false)}
                  className="bg-[#2d5016] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#f4a261] transition-colors duration-200 shadow-lg hover:shadow-xl"
                >
                  Got it!
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Booking Form */}
          <div className="bg-white rounded-xl shadow-lg p-6 pb-6">
            <h2 className="text-2xl font-bold text-[#2d5016] mb-4">
              Schedule Your Session
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4 mb-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    required
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
                  />
                </div>
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                    Country of Residence *
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    required
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="consultationDate" className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Consultation Date *
                  </label>
                  <input
                    type="date"
                    id="consultationDate"
                    name="consultationDate"
                    required
                    min={today}
                    value={formData.consultationDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
                  />
                </div>
                <div>
                  <label htmlFor="serviceInterest" className="block text-sm font-medium text-gray-700 mb-2">
                    Service Interest *
                  </label>
                  <select
                    id="serviceInterest"
                    name="serviceInterest"
                    required
                    value={formData.serviceInterest}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
                  >
                    <option value="">Select a service</option>
                    {services.map((service, index) => (
                      <option key={index} value={service}>
                        {service}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Tell us about your goals and interests
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={3}
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2d5016] focus:border-transparent"
                  placeholder="Share your business ideas, target markets, or any specific questions you have..."
                />
              </div>

              {/* reCAPTCHA */}
              <div className="flex justify-center">
                <ReCAPTCHA
                  ref={recaptchaRef}
                  sitekey={RECAPTCHA_CONFIG.SITE_KEY}
                  onChange={handleRecaptchaChange}
                  onExpired={handleRecaptchaExpired}
                  theme="light"
                />
              </div>

              {/* Message d'erreur */}
              {submitError && (
                <div className="flex items-center p-4 bg-red-100 border border-red-300 rounded-lg text-red-700">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  {submitError}
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full py-4 px-6 rounded-lg font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl flex items-center justify-center ${
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#2d5016] text-white hover:bg-[#f4a261]'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Envoi en cours...
                  </>
                ) : (
                  <>
                    <Send className="h-5 w-5 mr-2" />
                    Book My Consultation
                  </>
                )}
              </button>
            </form>
          </div>

          {/* Service Information */}
          <div className="space-y-8">
            {/* Service Options */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-[#2d5016] mb-6">
                Service Options
              </h3>
              
              <div className="space-y-6">
                <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-green-800">Discovery Session</h4>
                    <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">FREE</span>
                  </div>
                  <p className="text-green-700 text-sm mb-3">
                    30-minute introductory session to understand your goals and explore how we can help.
                  </p>
                  <ul className="text-green-600 text-sm space-y-1">
                    <li>• Initial goal assessment</li>
                    <li>• Overview of our services</li>
                    <li>• Basic market insights</li>
                    <li>• Next steps recommendation</li>
                  </ul>
                </div>

                <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-blue-800">Full Consultation</h4>
                    <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">PAID</span>
                  </div>
                  <p className="text-blue-700 text-sm mb-3">
                    Comprehensive 60-90 minute strategic session with detailed analysis and actionable recommendations.
                  </p>
                  <ul className="text-blue-600 text-sm space-y-1">
                    <li>• In-depth market analysis</li>
                    <li>• Customized business strategy</li>
                    <li>• Risk assessment</li>
                    <li>• Detailed action plan</li>
                    <li>• Follow-up resources</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Contact Information & What to Expect - Combined */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-[#2d5016] mb-6">
                Contact Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-[#2d5016] mr-3 mt-1" />
                    <div>
                      <h4 className="font-medium text-gray-900">Address</h4>
                      <p className="text-gray-600">539 W. Commerce St #7016<br />Dallas, TX 75208</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Phone className="h-5 w-5 text-[#2d5016] mr-3 mt-1" />
                    <div>
                      <h4 className="font-medium text-gray-900">Phone</h4>
                      <p className="text-gray-600">
                        <a href="tel:+12029267048" className="hover:text-[#f4a261] transition-colors">
                          +****************
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Mail className="h-5 w-5 text-[#2d5016] mr-3 mt-1" />
                    <div>
                      <h4 className="font-medium text-gray-900">Email</h4>
                      <p className="text-gray-600">
                        <a href="mailto:<EMAIL>" className="hover:text-[#f4a261] transition-colors">
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Clock className="h-5 w-5 text-[#2d5016] mr-3 mt-1" />
                    <div>
                      <h4 className="font-medium text-gray-900">Business Hours</h4>
                      <p className="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM CST</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-6">
                <h4 className="text-lg font-semibold text-[#2d5016] mb-4">What to Expect</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-gray-700 text-sm">
                      We'll contact you within 24 hours to confirm your consultation
                    </p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-gray-700 text-sm">
                      Personalized session tailored to your specific goals
                    </p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-gray-700 text-sm">
                      Actionable recommendations and clear next steps
                    </p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#f4a261] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-gray-700 text-sm">
                      Follow-up resources and ongoing support options
                    </p>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* Our Location - Full Width at Bottom */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mt-12">
          <div className="p-6 border-b border-gray-200 text-center">
            <h3 className="text-2xl font-semibold text-[#2d5016]">Our Location</h3>
            <p className="text-gray-600 mt-2">Visit us at our Dallas office</p>
          </div>
          <div className="h-80">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3355.2847!2d-96.8097!3d32.7767!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x864e991b9685b3e9%3A0x8b1b1b1b1b1b1b1b!2s539%20W%20Commerce%20St%20%237016%2C%20Dallas%2C%20TX%2075208!5e0!3m2!1sen!2sus!4v1234567890"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="Afripath Consulting Location"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookConsultation;

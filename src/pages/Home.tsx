import React from 'react';
import { Link } from 'react-router-dom';
import { Search, MapPin, FileText, Users, Plane, ArrowRight } from 'lucide-react';
import BackgroundAnimation from '../components/BackgroundAnimation';
import { useContent, getContentValue, getContentArray } from '../hooks/useContent';

const Home = () => {
  const { content, loading, error } = useContent('home');

  // Fallback services si l'API n'est pas disponible
  const services = [
    {
      icon: <Search className="h-8 w-8" />,
      title: 'Opportunity Mapping & Market Discovery',
      description: 'Our opportunity mapping & market discovery service offers a strategic approach to African market entry consulting, helping you uncover where your business can succeed. This is the first step toward starting a business in Africa with clarity, not complexity.'
    },
    {
      icon: <FileText className="h-8 w-8" />,
      title: 'Industry Deep Dives & Sector Intelligence',
      description: 'Our in-depth industry intelligence reports are your key to low-risk investment sectors in Africa. Whether you\'re new to investing or expanding an existing portfolio, our reports help you act decisively in promising markets. Explore where the real diaspora investment opportunities lie.'
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: 'Business Formation & Legal Setup',
      description: 'African business registration support doesn\'t need to be a hassle. Whether you\'re focused on Kenya, Ghana, or how to launch a business in Nigeria as a diaspora, we simplify the process and help you start strong, legally and efficiently.'
    },
    {
      icon: <MapPin className="h-8 w-8" />,
      title: 'Local Advisor Network & Onsite Support',
      description: 'Our local advisor network connects you with expert African business consultants for diaspora clients who need reliable, in-country representation.'
    },
    {
      icon: <Plane className="h-8 w-8" />,
      title: 'Immersive Business Immersion Tours',
      description: 'Discover real opportunities on the ground with our trade visit opportunities in Africa for investors. Walk away with not just ideas, but trusted contacts, insight, and a sharper understanding of how to invest in Africa with intention and impact.'
    }
  ];

  const stats = [
    { number: '300+', label: 'Projects Guided' },
    { number: '450+', label: 'Expert Partners & Local Advisors' },
    { number: '315+', label: 'Business Ventures' },
    { number: '6,500+', label: 'Diaspora Clients' }
  ];

  const testimonials = [
    {
      quote: "Afripath made what seemed impossible feel simple. Their local connections and deep knowledge of West African markets helped me launch my agribusiness in Ghana with confidence.",
      author: "Nia T",
      location: "Atlanta, GA"
    },
    {
      quote: "I had no idea where to start. Their market discovery report was a game-changer. Within months, I was registered and operational in Nairobi.",
      author: "Kofi B",
      location: "London, UK"
    },
    {
      quote: "The immersive trade visit was everything I needed: real connections, honest feedback, and a clear sense of where I fit in the market.",
      author: "Sandra E",
      location: "Toronto, Canada"
    },
    {
      quote: "I've worked with other consultants, but Afripath's understanding of both the diaspora mindset and African business culture is unmatched.",
      author: "Emeka J",
      location: "Houston, TX"
    },
    {
      quote: "From compliance to permits, they took care of every step. I couldn't have launched in South Africa without them.",
      author: "Malik R",
      location: "Dubai, UAE"
    }
  ];

  // Afficher un loader pendant le chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2d5016] mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du contenu...</p>
        </div>
      </div>
    );
  }

  // Afficher une erreur si le chargement échoue
  if (error) {
    console.warn('Erreur de chargement du contenu:', error);
    // Continuer avec le contenu par défaut
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative min-h-screen bg-cover bg-center bg-no-repeat overflow-hidden"
               style={{ backgroundImage: 'url(/hero2.jpg)' }}>
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black/60"></div>

        {/* Background Animation */}
        <BackgroundAnimation />

        <div className="relative z-10 min-h-screen flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">

              {/* Left Column - Text Content */}
              <div className="text-white space-y-6 lg:space-y-8 animate-fade-in-up text-center lg:text-left">
                <div className="space-y-2 lg:space-y-4">
                  <p className="text-[#f4a261] font-semibold text-base sm:text-lg tracking-wide uppercase animate-slide-in-left">
                    {getContentValue(content, 'hero', 'tagline', 'Bridge the Gap')}
                  </p>
                  <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight animate-slide-in-left animation-delay-200">
                    {getContentValue(content, 'hero', 'title', 'Turn Your Diaspora Dreams into')}
                    <span className="text-[#f4a261] block mt-1 lg:mt-2"> {getContentValue(content, 'hero', 'titleHighlight', 'African Business Success')}</span>
                  </h1>
                </div>

                <p className="text-lg sm:text-xl text-gray-200 leading-relaxed max-w-2xl mx-auto lg:mx-0 animate-slide-in-left animation-delay-400">
                  {getContentValue(content, 'hero', 'description', 'Explore trusted tools, insights, and expert support to confidently launch or grow your business in Africa, without confusion, guesswork, or costly mistakes.')}
                </p>

                <div className="flex flex-col sm:flex-row gap-4 items-center justify-center lg:justify-start animate-slide-in-left animation-delay-600">
                  <Link
                    to="/book-consultation"
                    className="w-full sm:w-auto inline-flex items-center justify-center bg-[#f4a261] text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-[#e8956a] transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    {getContentValue(content, 'hero', 'ctaButton', 'Book a Consultation')}
                    <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                  </Link>

                  <div className="flex items-center space-x-3 sm:space-x-4">
                    <div className="flex -space-x-1 sm:-space-x-2">
                      <img className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white" src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=100&h=100&fit=crop&crop=face" alt="Client" />
                      <img className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white" src="https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?w=100&h=100&fit=crop&crop=face" alt="Client" />
                      <img className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white" src="https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?w=100&h=100&fit=crop&crop=face" alt="Client" />
                      <img className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white" src="https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?w=100&h=100&fit=crop&crop=face" alt="Client" />
                    </div>
                    <div className="text-white">
                      <p className="text-xs sm:text-sm font-medium">Satisfied Clients</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Stats/Features */}
              <div className="lg:pl-12 animate-fade-in-right mt-8 lg:mt-0">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-white/20 mx-auto max-w-md lg:max-w-none">
                  <div className="grid grid-cols-2 gap-4 sm:gap-6">
                    <div className="text-center animate-count-up animation-delay-800">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-[#f4a261] mb-1 sm:mb-2">500+</div>
                      <div className="text-white text-xs sm:text-sm leading-tight">Businesses Launched</div>
                    </div>
                    <div className="text-center animate-count-up animation-delay-1000">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-[#f4a261] mb-1 sm:mb-2">25+</div>
                      <div className="text-white text-xs sm:text-sm leading-tight">African Countries</div>
                    </div>
                    <div className="text-center animate-count-up animation-delay-1200">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-[#f4a261] mb-1 sm:mb-2">95%</div>
                      <div className="text-white text-xs sm:text-sm leading-tight">Success Rate</div>
                    </div>
                    <div className="text-center animate-count-up animation-delay-1400">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-[#f4a261] mb-1 sm:mb-2">$50M+</div>
                      <div className="text-white text-xs sm:text-sm leading-tight">Investments Facilitated</div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

        {/* Scroll indicator - Hidden on mobile */}
        <div className="hidden sm:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-20 bg-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
              {getContentValue(content, 'services', 'sectionTitle', 'How We Help You Succeed')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getContentValue(content, 'services', 'sectionDescription', 'From initial discovery to ongoing support, we provide comprehensive guidance for your African business journey.')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {(getContentArray(content, 'services', 'items', []).length > 0
              ? getContentArray(content, 'services', 'items', [])
              : services
            ).map((service, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="text-[#2d5016] mb-4">{services[index]?.icon}</div>
                <h3 className="text-xl font-semibold text-[#2d5016] mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  {service.description}
                </p>
                {service.benefits && (
                  <ul className="text-sm text-gray-600 space-y-1">
                    {service.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-start">
                        <span className="text-[#f4a261] mr-2">•</span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              to="/services"
              className="inline-flex items-center text-[#2d5016] font-semibold hover:text-[#f4a261] transition-colors duration-200"
            >
              View All Services
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Banner */}
      <section className="py-16 bg-[#2d5016]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            {getContentArray(content, 'stats', 'items', stats).map((stat, index) => (
              <div key={index} className="text-white">
                <div className="text-4xl md:text-5xl font-bold mb-2">
                  {stat.number}
                </div>
                <div className="text-lg opacity-90">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-4">
              {getContentValue(content, 'testimonials', 'sectionTitle', 'Success Stories')}
            </h2>
            <p className="text-xl text-gray-600">
              {getContentValue(content, 'testimonials', 'sectionDescription', 'Real results from diaspora entrepreneurs we\'ve helped')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {getContentArray(content, 'testimonials', 'items', testimonials).map((testimonial, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-[#f4a261]"
              >
                <p className="text-gray-700 italic mb-4 leading-relaxed">
                  "{testimonial.quote}"
                </p>
                <div className="font-semibold text-[#2d5016]">
                  {testimonial.author}
                </div>
                <div className="text-gray-500 text-sm">
                  {testimonial.location}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-200">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-[#2d5016] mb-6">
            {getContentValue(content, 'cta', 'title', 'Ready to Start Your African Business Journey?')}
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            {getContentValue(content, 'cta', 'description', 'Schedule a consultation to discuss your goals and learn how we can help.')}
          </p>
          <Link
            to="/book-consultation"
            className="inline-flex items-center bg-[#2d5016] text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-[#f4a261] transition-all duration-300 transform hover:scale-105"
          >
            {getContentValue(content, 'cta', 'buttonText', 'Get Started Today')}
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Home;
{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:report": "playwright show-report"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@types/react-google-recaptcha": "^2.1.9", "lucide-react": "^0.536.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-google-recaptcha": "^3.1.0", "react-router-dom": "^7.7.1"}, "devDependencies": {"@eslint/js": "^9.32.0", "@playwright/test": "^1.54.2", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "~5.8.3", "typescript-eslint": "^8.38.0", "vite": "^7.0.6"}}
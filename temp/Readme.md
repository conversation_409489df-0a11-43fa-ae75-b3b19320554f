Contexte :
Mon site est développé en React + Tailwind CSS et hébergé sur Cloudflare (Pages/Workers).
Je souhaite un backend léger qui me permette de modifier facilement les textes de chaque page, sans toucher au code.
L’édition doit être intuitive pour un administrateur non technique.

Objectifs :

Créer un backend en Node.js + Express (ou Cloudflare Workers API) qui utilise un fichier JSON (GSON) comme source de données au lieu d’une base SQL.

Les textes doivent être organisés par nom de page et identifiant unique de bloc.

Ce fichier JSON est lu et mis à jour par le backend.

Mettre en place une API REST sécurisée avec :

GET /content/:page → retourne tous les textes d’une page.

PUT /content/:page/:blockId → met à jour le texte correspondant dans le JSON et sauvegarde le fichier.

Créer une interface /admin protégée par login/mot de passe.

Authentification simple (JWT ou session).

Dans /admin, proposer l’édition inline directement dans la page (utiliser contenteditable en React).

Un bouton "Enregistrer" envoie les modifications via l’API pour mise à jour dans le JSON.

Lors de l’affichage du site public, les textes sont toujours chargés depuis ce JSON.

Prévoir un mode preview/admin activé uniquement après authentification, pour éviter que le grand public puisse éditer.

Contraintes techniques :

Backend en Node.js + Express ou Cloudflare Workers (selon ce qui est le plus adapté).

Stockage des textes dans un fichier JSON unique au format UTF-8.

Frontend admin en React + Tailwind (même stack que le site).

Le code doit être bien structuré, facile à maintenir, et commenté.

Prévoir un script d’initialisation pour insérer les textes de départ.
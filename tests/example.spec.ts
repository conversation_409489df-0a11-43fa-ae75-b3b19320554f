import { test, expect } from '@playwright/test';

test('has title', async ({ page }) => {
  await page.goto('/');

  // Expect a title "to contain" a substring.
  await expect(page).toHaveTitle(/Afripath Consulting/);
});

test('navigation works', async ({ page }) => {
  await page.goto('/');

  // Click the About link.
  await page.click('text=About');

  // Expects the URL to contain about.
  await expect(page).toHaveURL(/.*about/);
});

test('home page loads correctly', async ({ page }) => {
  await page.goto('/');

  // Check if main heading is visible
  await expect(page.locator('h1')).toBeVisible();

  // Check if navigation menu is present
  await expect(page.locator('nav')).toBeVisible();
});

test('playwright functionality test', async ({ page }) => {
  await page.goto('/');

  // Test basic page interaction
  await expect(page).toHaveTitle(/Afripath Consulting/);

  // Test element visibility
  await expect(page.locator('body')).toBeVisible();

  // Test that we can take a screenshot
  await page.screenshot({ path: 'test-results/homepage-screenshot.png' });

  // Test viewport size
  const viewportSize = page.viewportSize();
  expect(viewportSize?.width).toBeGreaterThan(0);
  expect(viewportSize?.height).toBeGreaterThan(0);

  // Test page content
  const pageContent = await page.content();
  expect(pageContent).toContain('html');
});

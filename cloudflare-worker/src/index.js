/**
 * AfriPath CMS API Worker
 * Cloudflare Worker pour gérer le contenu du site via KV
 */

// Configuration CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // À restreindre en production
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
};

// Réponse CORS pour les requêtes OPTIONS
function handleCORS() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders,
  });
}

// Fonction pour ajouter les headers CORS à une réponse
function addCORSHeaders(response) {
  Object.keys(corsHeaders).forEach(key => {
    response.headers.set(key, corsHeaders[key]);
  });
  return response;
}

// Middleware d'authentification simple (à améliorer en production)
async function authenticate(request) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }
  
  const token = authHeader.substring(7);
  // Pour l'instant, token simple - à remplacer par JWT en production
  return token === 'admin-token-2025'; // À changer !
}

// Initialiser le contenu par défaut
const defaultContent = {
  pages: {
    home: {
      hero: {
        tagline: "Bridge the Gap",
        title: "Turn Your Diaspora Dreams into",
        titleHighlight: "African Business Success",
        description: "Explore trusted tools, insights, and expert support to confidently launch or grow your business in Africa, without confusion, guesswork, or costly mistakes.",
        ctaButton: "Book a Consultation"
      },
      services: {
        sectionTitle: "How We Help You Succeed",
        sectionDescription: "From initial discovery to ongoing support, we provide comprehensive guidance for your African business journey.",
        items: [
          {
            title: "Opportunity Mapping & Market Discovery",
            description: "Our opportunity mapping & market discovery service offers a strategic approach to African market entry consulting, helping you uncover where your business can succeed. This is the first step toward starting a business in Africa with clarity, not complexity.",
            benefits: [
              "Strategic market entry consulting",
              "Verified business opportunity identification",
              "Country and sector analysis",
              "Risk assessment and mitigation strategies"
            ]
          },
          {
            title: "Industry Deep Dives & Sector Intelligence",
            description: "Our in-depth industry intelligence reports are your key to low-risk investment sectors in Africa. Whether you're new to investing or expanding an existing portfolio, our reports help you act decisively in promising markets.",
            benefits: [
              "Comprehensive industry analysis",
              "Low-risk investment sector identification",
              "Market trends and growth projections",
              "Competitive landscape assessment"
            ]
          },
          {
            title: "Business Formation & Legal Setup",
            description: "African business registration support doesn't need to be a hassle. Whether you're focused on Kenya, Ghana, or how to launch a business in Nigeria as a diaspora, we simplify the process and help you start strong, legally and efficiently.",
            benefits: [
              "Streamlined business registration process",
              "Legal compliance and regulatory guidance",
              "Tax optimization strategies",
              "Permit and licensing assistance"
            ]
          },
          {
            title: "Local Advisor Network & Onsite Support",
            description: "Our local advisor network connects you with expert African business consultants for diaspora clients who need reliable, in-country representation.",
            benefits: [
              "Expert African business consultants",
              "Reliable in-country representation",
              "Cultural and business mentorship",
              "Ongoing operational support"
            ]
          },
          {
            title: "Immersive Business Immersion Tours",
            description: "Discover real opportunities on the ground with our trade visit opportunities in Africa for investors. Walk away with not just ideas, but trusted contacts, insight, and a sharper understanding of how to invest in Africa with intention and impact.",
            benefits: [
              "Real on-ground opportunity discovery",
              "Trusted contact development",
              "Market insight and intelligence",
              "Strategic investment guidance"
            ]
          }
        ]
      },
      stats: {
        items: [
          { number: "500+", label: "Businesses Launched" },
          { number: "25+", label: "African Countries" },
          { number: "95%", label: "Success Rate" },
          { number: "$50M+", label: "Investments Facilitated" }
        ]
      },
      testimonials: {
        sectionTitle: "Success Stories",
        sectionDescription: "Real results from diaspora entrepreneurs we've helped",
        items: [
          {
            quote: "Afripath made what seemed impossible feel simple. Their local connections and deep knowledge of West African markets helped me launch my agribusiness in Ghana with confidence.",
            author: "Nia T",
            location: "Atlanta, GA"
          },
          {
            quote: "I had no idea where to start. Their market discovery report was a game-changer. Within months, I was registered and operational in Nairobi.",
            author: "Kofi B",
            location: "London, UK"
          },
          {
            quote: "The immersive trade visit was everything I needed: real connections, honest feedback, and a clear sense of where I fit in the market.",
            author: "Sandra E",
            location: "Toronto, Canada"
          },
          {
            quote: "I've worked with other consultants, but Afripath's understanding of both the diaspora mindset and African business culture is unmatched.",
            author: "Emeka J",
            location: "Houston, TX"
          },
          {
            quote: "From compliance to permits, they took care of every step. I couldn't have launched in South Africa without them.",
            author: "Malik R",
            location: "Dubai, UAE"
          }
        ]
      },
      cta: {
        title: "Ready to Start Your African Business Journey?",
        description: "Schedule a consultation to discuss your goals and learn how we can help.",
        buttonText: "Get Started Today"
      }
    },
    about: {
      hero: {
        title: "About Us",
        description: "AfriPath Consulting is a diaspora-led firm dedicated to helping individuals and organizations confidently invest and do business in Africa."
      },
      mission: {
        title: "Our Mission",
        description: "Our mission is to empower the African diaspora with trusted guidance, local expertise, and strategic tools to confidently launch and grow sustainable businesses across the continent."
      },
      vision: {
        title: "Our Vision",
        description: "Our vision is to become the leading consulting firm connecting the global African diaspora with vibrant, thriving markets in Africa."
      }
    },
    services: {
      hero: {
        title: "Services & Resources",
        description: "Expert guidance and comprehensive resources for African diaspora professionals and entrepreneurs looking to invest and build successful businesses across Africa."
      },
      services: {
        sectionTitle: "Our Services",
        sectionDescription: "Comprehensive support for African diaspora entrepreneurs and investors looking to build successful businesses across Africa.",
        items: [
          {
            title: "Opportunity Mapping & Market Discovery",
            description: "We help you uncover the best business opportunities in Africa by mapping key industries, emerging trends, and high-growth regions tailored to your goals."
          },
          {
            title: "Industry Deep Dives & Sector Intelligence",
            description: "Go beyond surface-level data with our in-depth industry analyses tailored to diaspora investors."
          },
          {
            title: "Business Formation & Legal Setup",
            description: "We simplify the complex process of starting a business in Africa. From company registration and regulatory compliance to tax ID acquisition."
          },
          {
            title: "Local Advisor Network & Onsite Support",
            description: "AfriPath connects you to a trusted network of local advisors, consultants, and fixers who understand the landscape."
          },
          {
            title: "Immersive Business Immersion Tours",
            description: "Our Business Immersion Tours offer diaspora investors and entrepreneurs a powerful firsthand experience of doing business in Africa."
          }
        ]
      },
      cta: {
        title: "Ready to Get Started?",
        description: "Book a consultation to discuss your African business goals.",
        buttonText: "Book Now"
      }
    },
    kenya: {
      hero: {
        title: "Kenya",
        subtitle: "East Africa's Economic Powerhouse",
        description: "Kenya is East Africa's economic powerhouse with a diversified economy driven by services, agriculture, and industry."
      },
      overview: {
        title: "Economic Overview",
        description: "Kenya is East Africa's economic powerhouse with a diversified economy driven by services, agriculture, and industry. Known as the \"Silicon Savannah,\" Kenya leads the continent in mobile money innovation and has a thriving tech ecosystem that attracts global investors and entrepreneurs.",
        stats: [
          { label: "GDP (2023)", value: "$115.49 billion" },
          { label: "Currency", value: "Kenyan Shilling (KES)" },
          { label: "Population", value: "54.0 million" },
          { label: "Languages", value: "English, Swahili" }
        ]
      },
      investment: {
        title: "Investment Climate",
        items: [
          "Strategic location as gateway to East Africa",
          "Skilled workforce and business-friendly policies",
          "Leading fintech and mobile money innovation",
          "Strong democratic institutions and rule of law"
        ]
      },
      diaspora: {
        title: "Diaspora Opportunities",
        description: "Kenya has a large and active diaspora, particularly in the US, UK, and other developed countries. The government actively encourages diaspora investment and participation in development.",
        incentives: [
          "Kenya Diaspora Investment Incentives",
          "Diaspora bonds and investment programs",
          "Dual citizenship allowed"
        ],
        opportunities: [
          "Real estate and housing development",
          "Technology and fintech startups",
          "Agriculture and agribusiness",
          "Tourism and hospitality"
        ]
      }
    },
    cameroon: {
      hero: {
        title: "Cameroon",
        subtitle: "Central Africa's Gateway",
        description: "Cameroon serves as a strategic gateway to Central Africa with diverse economic opportunities and rich natural resources."
      },
      overview: {
        title: "Economic Overview",
        description: "Cameroon is one of Central Africa's most diversified economies, with significant oil, agriculture, and manufacturing sectors. The country benefits from both Atlantic coast access and strategic location in Central Africa.",
        stats: [
          { label: "GDP (2023)", value: "$45.24 billion" },
          { label: "Currency", value: "Central African CFA Franc (XAF)" },
          { label: "Population", value: "27.9 million" },
          { label: "Languages", value: "French, English" }
        ]
      },
      investment: {
        title: "Investment Climate",
        items: [
          "Gateway to Central African markets",
          "Bilingual business environment (French/English)",
          "Rich natural resources (oil, minerals, timber)",
          "Diversified economy with multiple sectors",
          "Strategic port access via Douala"
        ]
      },
      sectors: {
        title: "Key Investment Sectors",
        items: [
          {
            name: "Oil & Gas",
            description: "Exploration, refining, and petrochemical opportunities"
          },
          {
            name: "Agriculture",
            description: "Cocoa, coffee, cotton, and food processing"
          },
          {
            name: "Mining",
            description: "Bauxite, iron ore, and other mineral extraction"
          },
          {
            name: "Manufacturing",
            description: "Textiles, food processing, and consumer goods"
          }
        ]
      },
      diaspora: {
        title: "Diaspora Opportunities",
        description: "Cameroon has a significant diaspora community and offers various opportunities for investment and business development.",
        incentives: [
          "Investment promotion incentives",
          "Dual citizenship allowed",
          "Special economic zones available",
          "Government support for diaspora projects"
        ],
        opportunities: [
          "Agricultural modernization projects",
          "Infrastructure development",
          "Manufacturing and processing",
          "Technology and telecommunications",
          "Tourism and hospitality"
        ]
      }
    },
    senegal: {
      hero: {
        title: "Senegal",
        subtitle: "West Africa's Stable Democracy",
        description: "Senegal is known for its political stability, strategic location, and growing economy in West Africa."
      },
      overview: {
        title: "Economic Overview",
        description: "Senegal has maintained steady economic growth and is considered one of the most stable democracies in Africa. The country has significant potential in agriculture, fishing, and emerging oil and gas sectors.",
        stats: [
          { label: "GDP (2023)", value: "$27.68 billion" },
          { label: "Currency", value: "West African CFA Franc (XOF)" },
          { label: "Population", value: "17.3 million" },
          { label: "Languages", value: "French, Wolof" }
        ]
      },
      investment: {
        title: "Investment Climate",
        items: [
          "Stable democratic institutions",
          "Strategic Atlantic coast location",
          "Emerging oil and gas discoveries",
          "Strong agricultural and fishing sectors",
          "Growing services and tourism industry"
        ]
      },
      sectors: {
        title: "Key Investment Sectors",
        items: [
          {
            name: "Oil & Gas",
            description: "Emerging offshore oil and gas development opportunities"
          },
          {
            name: "Agriculture & Fishing",
            description: "Peanuts, rice, fishing, and aquaculture development"
          },
          {
            name: "Tourism",
            description: "Cultural tourism, beach resorts, and eco-tourism"
          },
          {
            name: "Manufacturing",
            description: "Food processing, textiles, and light manufacturing"
          }
        ]
      },
      diaspora: {
        title: "Diaspora Opportunities",
        description: "Senegal has a large diaspora community, particularly in France and the US, and actively promotes diaspora investment.",
        incentives: [
          "Diaspora investment promotion programs",
          "Tax incentives for priority investments",
          "Simplified procedures for diaspora investors",
          "Government support for diaspora projects"
        ],
        opportunities: [
          "Agricultural value chains",
          "Tourism and hospitality",
          "Real estate development",
          "Technology and digital services",
          "Renewable energy projects"
        ]
      }
    },
    ivorycoast: {
      hero: {
        title: "Côte d'Ivoire",
        subtitle: "Economic Engine of West Africa",
        description: "Côte d'Ivoire is the largest economy in West Africa's monetary union and a major agricultural exporter."
      },
      overview: {
        title: "Economic Overview",
        description: "Côte d'Ivoire is the world's largest cocoa producer and has experienced strong economic growth in recent years. The country has implemented significant economic reforms and infrastructure development programs.",
        stats: [
          { label: "GDP (2023)", value: "$70.99 billion" },
          { label: "Currency", value: "West African CFA Franc (XOF)" },
          { label: "Population", value: "28.2 million" },
          { label: "Languages", value: "French" }
        ]
      },
      investment: {
        title: "Investment Climate",
        items: [
          "Largest economy in WAEMU monetary union",
          "World's leading cocoa and cashew producer",
          "Strategic port access via Abidjan",
          "Growing manufacturing and services sectors",
          "Government investment incentives available"
        ]
      },
      sectors: {
        title: "Key Investment Sectors",
        items: [
          {
            name: "Agriculture & Agribusiness",
            description: "Cocoa, coffee, cashew nuts, and food processing opportunities"
          },
          {
            name: "Manufacturing",
            description: "Textiles, automotive assembly, and consumer goods production"
          },
          {
            name: "Infrastructure",
            description: "Transportation, energy, and telecommunications development"
          },
          {
            name: "Services",
            description: "Financial services, tourism, and digital economy growth"
          }
        ]
      },
      diaspora: {
        title: "Diaspora Opportunities",
        description: "Côte d'Ivoire actively encourages diaspora investment and has established programs to facilitate business creation and investment.",
        incentives: [
          "Investment promotion agency support",
          "Tax incentives for priority sectors",
          "Simplified business registration process",
          "Diaspora investment facilitation programs"
        ],
        opportunities: [
          "Agricultural value chain development",
          "Manufacturing and industrial projects",
          "Real estate and construction",
          "Technology and digital services",
          "Tourism and hospitality"
        ]
      }
    },
    morocco: {
      hero: {
        title: "Morocco",
        subtitle: "Gateway to Africa and Europe",
        description: "Morocco offers unique access to both African and European markets with modern infrastructure and business-friendly policies."
      },
      overview: {
        title: "Economic Overview",
        description: "Morocco has developed a diversified economy with strong manufacturing, agriculture, and services sectors. The country has invested heavily in infrastructure and renewable energy.",
        stats: [
          { label: "GDP (2023)", value: "$134.18 billion" },
          { label: "Currency", value: "Moroccan Dirham (MAD)" },
          { label: "Population", value: "37.5 million" },
          { label: "Languages", value: "Arabic, Berber, French" }
        ]
      },
      investment: {
        title: "Investment Climate",
        items: [
          "Strategic location between Africa and Europe",
          "Modern infrastructure and logistics",
          "Free trade agreements with EU and US",
          "Growing renewable energy sector",
          "Stable political and economic environment"
        ]
      },
      sectors: {
        title: "Key Investment Sectors",
        items: [
          {
            name: "Automotive",
            description: "Major automotive manufacturing hub for global brands"
          },
          {
            name: "Renewable Energy",
            description: "Solar and wind energy projects and manufacturing"
          },
          {
            name: "Agriculture",
            description: "Citrus fruits, vegetables, and agricultural exports"
          },
          {
            name: "Tourism",
            description: "Cultural tourism, luxury resorts, and hospitality"
          },
          {
            name: "Aerospace",
            description: "Aircraft manufacturing and maintenance services"
          }
        ]
      },
      diaspora: {
        title: "Diaspora Opportunities",
        description: "Morocco has one of the largest diaspora communities globally and offers comprehensive support for diaspora investment and business development.",
        incentives: [
          "Comprehensive diaspora investment programs",
          "Tax advantages for diaspora investors",
          "Dual nationality allowed",
          "Special investment zones and incentives"
        ],
        opportunities: [
          "Manufacturing and export industries",
          "Tourism and real estate development",
          "Renewable energy projects",
          "Technology and innovation hubs",
          "Agricultural modernization"
        ]
      }
    }
  },
  global: {
    company: {
      name: "AfriPath Consulting",
      logo: "/logo.png"
    },
    navigation: [
      { name: "Home", href: "/" },
      { name: "About", href: "/about" },
      { name: "Services", href: "/services" },
      { name: "Country Profiles", href: "/country-profiles" }
    ]
  },
  metadata: {
    lastUpdated: new Date().toISOString(),
    version: "1.0.0"
  }
};

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;

    // Gérer les requêtes CORS
    if (method === 'OPTIONS') {
      return handleCORS();
    }

    try {
      // Route: GET /content/:page - Récupérer le contenu d'une page
      if (method === 'GET' && path.startsWith('/content/')) {
        const page = path.split('/')[2];
        
        if (!page) {
          return addCORSHeaders(new Response(JSON.stringify({ error: 'Page parameter required' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }));
        }

        // Récupérer le contenu depuis KV
        let content = await env.SITE_CONTENT.get('site-data', 'json');
        
        // Si pas de contenu, initialiser avec le contenu par défaut
        if (!content) {
          await env.SITE_CONTENT.put('site-data', JSON.stringify(defaultContent));
          content = defaultContent;
        }

        // Retourner le contenu de la page demandée
        const pageContent = content.pages[page];
        if (!pageContent) {
          return addCORSHeaders(new Response(JSON.stringify({ error: 'Page not found' }), {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          }));
        }

        return addCORSHeaders(new Response(JSON.stringify(pageContent), {
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      // Route: GET /content - Récupérer tout le contenu
      if (method === 'GET' && path === '/content') {
        let content = await env.SITE_CONTENT.get('site-data', 'json');
        
        if (!content) {
          await env.SITE_CONTENT.put('site-data', JSON.stringify(defaultContent));
          content = defaultContent;
        }

        return addCORSHeaders(new Response(JSON.stringify(content), {
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      // Route: PUT /content/:page/:blockId - Mettre à jour un bloc de contenu
      if (method === 'PUT' && path.startsWith('/content/')) {
        // Vérifier l'authentification
        if (!(await authenticate(request))) {
          return addCORSHeaders(new Response(JSON.stringify({ error: 'Unauthorized' }), {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          }));
        }

        const pathParts = path.split('/');
        const page = pathParts[2];
        const blockId = pathParts[3];

        if (!page || !blockId) {
          return addCORSHeaders(new Response(JSON.stringify({ error: 'Page and blockId parameters required' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }));
        }

        // Récupérer le nouveau contenu depuis le body
        const newContent = await request.json();

        // Récupérer le contenu actuel
        let content = await env.SITE_CONTENT.get('site-data', 'json');
        if (!content) {
          content = defaultContent;
        }

        // Vérifier que la page existe
        if (!content.pages[page]) {
          return addCORSHeaders(new Response(JSON.stringify({ error: 'Page not found' }), {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          }));
        }

        // Mettre à jour le bloc
        content.pages[page][blockId] = newContent;
        content.metadata.lastUpdated = new Date().toISOString();

        // Sauvegarder dans KV
        await env.SITE_CONTENT.put('site-data', JSON.stringify(content));

        return addCORSHeaders(new Response(JSON.stringify({ 
          success: true, 
          message: 'Content updated successfully',
          updatedBlock: { page, blockId, content: newContent }
        }), {
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      // Route: POST /init - Initialiser le contenu (pour le développement)
      if (method === 'POST' && path === '/init') {
        await env.SITE_CONTENT.put('site-data', JSON.stringify(defaultContent));
        return addCORSHeaders(new Response(JSON.stringify({ 
          success: true, 
          message: 'Content initialized successfully' 
        }), {
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      // Route par défaut
      return addCORSHeaders(new Response(JSON.stringify({ 
        error: 'Route not found',
        availableRoutes: [
          'GET /content/:page',
          'GET /content', 
          'PUT /content/:page/:blockId',
          'POST /init'
        ]
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      }));

    } catch (error) {
      console.error('Worker error:', error);
      return addCORSHeaders(new Response(JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }));
    }
  },
};

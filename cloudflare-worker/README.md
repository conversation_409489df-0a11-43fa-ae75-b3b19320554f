# AfriPath CMS API Worker

API Cloudflare Worker pour gérer le contenu du site AfriPath via KV storage.

## Configuration

### 1. Créer le namespace KV

1. Allez dans votre dashboard Cloudflare
2. **Workers & Pages** → **KV**
3. **Create a namespace** : `SITE_CONTENT`
4. Copiez l'ID du namespace

### 2. Configurer wrangler.toml

Remplacez `YOUR_KV_NAMESPACE_ID` dans `wrangler.toml` par l'ID de votre namespace.

### 3. Installation et déploiement

```bash
# Installer les dépendances
npm install

# Développement local
npm run dev

# Déployer en production
npm run deploy
```

## API Endpoints

### GET /content/:page
Récupère le contenu d'une page spécifique.

**Exemple :**
```bash
curl https://your-worker.your-subdomain.workers.dev/content/home
```

### GET /content
Récupère tout le contenu du site.

### PUT /content/:page/:blockId
Met à jour un bloc de contenu spécifique (nécessite authentification).

**Headers requis :**
```
Authorization: Bearer admin-token-2025
Content-Type: application/json
```

**Exemple :**
```bash
curl -X PUT \
  -H "Authorization: Bearer admin-token-2025" \
  -H "Content-Type: application/json" \
  -d '{"title": "Nouveau titre"}' \
  https://your-worker.your-subdomain.workers.dev/content/home/<USER>
```

### POST /init
Initialise le contenu par défaut (développement uniquement).

## Structure des données

Le contenu est organisé par pages et blocs :

```json
{
  "pages": {
    "home": {
      "hero": {
        "title": "Titre principal",
        "description": "Description"
      }
    }
  },
  "global": {
    "company": {
      "name": "AfriPath Consulting"
    }
  }
}
```

## Sécurité

⚠️ **Important** : Le token d'authentification actuel est temporaire. En production :

1. Implémentez JWT avec une clé secrète
2. Restreignez les CORS origins
3. Ajoutez une validation des données
4. Implémentez un système de rôles

## Développement

Le Worker inclut :
- ✅ Support CORS
- ✅ Authentification basique
- ✅ Gestion d'erreurs
- ✅ Initialisation automatique
- ✅ Structure JSON extensible
